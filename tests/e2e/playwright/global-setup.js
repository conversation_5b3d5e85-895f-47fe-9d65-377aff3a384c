import { chromium } from '@playwright/test';
import { authenticator } from 'otplib';
import { mkdirSync } from 'fs';
import { dirname } from 'path';

const testAccount = {
    secret: 'UX7E3PXBUKZE4VIB',
    email: '<EMAIL>',
    password: 'cladmin'
};

async function globalSetup() {
    console.log('Setting up global authentication...');
    console.log(`Environment: ${process.env.CI ? 'CI' : 'Local'}`);

    // Ensure the auth directory exists
    const authPath = 'tests/e2e/playwright/.auth/user.json';
    const authDir = dirname(authPath);
    try {
        mkdirSync(authDir, { recursive: true });
        console.log(`Created auth directory: ${authDir}`);
    } catch (error) {
        console.log(`Auth directory already exists or creation failed: ${error.message}`);
    }

    const browser = await chromium.launch({
        // Use headless mode in CI, can be visible locally for debugging
        headless: process.env.CI ? true : false
    });
    const context = await browser.newContext();
    const page = await context.newPage();

    // Add console logging for debugging
    page.on('console', msg => console.log(`PAGE LOG: ${msg.text()}`));
    page.on('pageerror', error => console.log(`PAGE ERROR: ${error.message}`));

    try {
        // Navigate to login page
        await page.goto('http://localhost:8000/login');
        
        // Fill in email and password
        await page.fill('input[name="email"]', testAccount.email);
        await page.fill('input[name="password"]', testAccount.password);
        
        // Click login button
        await page.click('button[type="submit"]');
        
        // Wait for TOTP field to appear with longer timeout in CI
        const totpTimeout = process.env.CI ? 60000 : 30000;
        await page.waitForSelector('input[name="one_time_password"]', { timeout: totpTimeout });
        console.log('TOTP field appeared');
        
        // Generate TOTP code
        const totp = authenticator.generate(testAccount.secret);
        console.log(`Generated TOTP for global setup: ${totp}`);
        
        // Fill in TOTP
        await page.fill('input[name="one_time_password"]', totp);
        
        // Trigger input event
        await page.dispatchEvent('input[name="one_time_password"]', 'input');
        
        // Click authenticate button first, then wait for response
        await page.click('button[data-testid="authenticate-btn"]');

        // Wait for either successful redirect or 2FA response
        try {
            // Try to wait for the 2FA verification response
            const verifyTotpPromise = page.waitForResponse(response =>
                response.url().includes('/2fa/2faVerify') && response.request().method() === 'POST',
                { timeout: 10000 }
            );

            const verifyResponse = await verifyTotpPromise;
            console.log(`Global setup 2FA verification status: ${verifyResponse.status()}`);
        } catch (error) {
            console.log('2FA response timeout, checking if authentication succeeded anyway...');
        }

        // Wait a bit for the session to be established (longer in CI)
        const sessionTimeout = process.env.CI ? 10000 : 5000;
        await page.waitForTimeout(sessionTimeout);

        // Navigate to home to ensure we're authenticated
        await page.goto('http://localhost:8000/home');

        // Wait for home page to load and verify we're authenticated
        await page.waitForLoadState('networkidle');

        // Check if we're actually on the home page (not redirected to login)
        const currentUrl = page.url();
        if (currentUrl.includes('/login') || currentUrl.includes('/2fa')) {
            throw new Error(`Authentication failed - still on auth page: ${currentUrl}`);
        }

        console.log(`Successfully authenticated - current URL: ${currentUrl}`);

        // Save the authentication state
        await context.storageState({ path: authPath });
        console.log('Global authentication setup complete');
        
    } catch (error) {
        console.error('Global setup failed:', error);
        throw error;
    } finally {
        await browser.close();
    }
}

export default globalSetup;
